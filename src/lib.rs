pub mod types;
pub mod utils;

use wasm_bindgen::prelude::*;

pub use types::*;

#[wasm_bindgen]
pub fn decode_entries(slot: u64, bytes: &[u8]) -> Result<ParsedEntry, JsValue> {
    decode_entries_internal(slot, bytes).map_err(|e| JsValue::from_str(&e))
}

// Internal function for testing and core logic
pub fn decode_entries_internal(slot: u64, bytes: &[u8]) -> Result<ParsedEntry, String> {
    if bytes.is_empty() {
        return Err("Empty data provided".to_string());
    }

    let entries: Vec<types::Entry> =
        bincode::deserialize(bytes).map_err(|e| format!("Failed to deserialize entries: {}", e))?;

    Ok(ParsedEntry { slot, entries })
}
