use serde::de::{self, SeqAccess, Unexpected, Visitor};
use serde::{Deserialize, Deserializer, Serialize};
use solana_hash::Hash;
use solana_short_vec as short_vec;
use std::fmt;

use crate::types::accounts::Pubkey;
use crate::types::instructions::CompiledInstruction;
use crate::types::messages::{LegacyMessage, MessageHeader, VersionedMessage, MESSAGE_VERSION_PREFIX};

pub enum MessagePrefix {
    Legacy(u8),
    Versioned(u8),
}

impl<'de> Deserialize<'de> for MessagePrefix {
    fn deserialize<D>(deserializer: D) -> Result<MessagePrefix, D::Error>
    where
        D: Deserializer<'de>,
    {
        struct PrefixVisitor;

        impl Visitor<'_> for PrefixVisitor {
            type Value = MessagePrefix;

            fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
                formatter.write_str("message prefix byte")
            }

            fn visit_u64<E: de::Error>(self, value: u64) -> Result<MessagePrefix, E> {
                if value > u8::MAX as u64 {
                    Err(de::Error::invalid_type(Unexpected::Unsigned(value), &self))?;
                }

                let byte = value as u8;
                if byte & MESSAGE_VERSION_PREFIX != 0 {
                    Ok(MessagePrefix::Versioned(byte & !MESSAGE_VERSION_PREFIX))
                } else {
                    Ok(MessagePrefix::Legacy(byte))
                }
            }
        }

        deserializer.deserialize_u8(PrefixVisitor)
    }
}

pub struct MessageVisitor;

impl<'de> Visitor<'de> for MessageVisitor {
    type Value = VersionedMessage;

    fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("message bytes")
    }

    fn visit_seq<A>(self, mut seq: A) -> Result<VersionedMessage, A::Error>
    where
        A: SeqAccess<'de>,
    {
        let prefix: MessagePrefix = seq.next_element()?.ok_or_else(|| de::Error::invalid_length(0, &self))?;

        match prefix {
            MessagePrefix::Legacy(num_required_signatures) => {
                #[derive(Serialize, Deserialize)]
                struct RemainingLegacyMessage {
                    pub num_readonly_signed_accounts: u8,
                    pub num_readonly_unsigned_accounts: u8,
                    #[serde(with = "short_vec")]
                    pub account_keys: Vec<Pubkey>,
                    pub recent_blockhash: Hash,
                    #[serde(with = "short_vec")]
                    pub instructions: Vec<CompiledInstruction>,
                }

                let message: RemainingLegacyMessage =
                    seq.next_element()?.ok_or_else(|| de::Error::invalid_length(1, &self))?;

                Ok(VersionedMessage::Legacy(LegacyMessage {
                    header: MessageHeader {
                        num_required_signatures,
                        num_readonly_signed_accounts: message.num_readonly_signed_accounts,
                        num_readonly_unsigned_accounts: message.num_readonly_unsigned_accounts,
                    },
                    account_keys: message.account_keys,
                    recent_blockhash: message.recent_blockhash,
                    instructions: message.instructions,
                }))
            }
            MessagePrefix::Versioned(version) => match version {
                0 => Ok(VersionedMessage::V0(seq.next_element()?.ok_or_else(|| de::Error::invalid_length(1, &self))?)),
                127 => Err(de::Error::custom("off-chain messages are not accepted")),
                _ => Err(de::Error::invalid_value(
                    de::Unexpected::Unsigned(version as u64),
                    &"a valid transaction message version",
                )),
            },
        }
    }
}
