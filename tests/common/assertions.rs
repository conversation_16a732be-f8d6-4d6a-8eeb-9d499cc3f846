#[macro_export]
macro_rules! assert_binary_compatible {
    ($custom:expr, $reference:expr) => {
        assert_eq!($custom, $reference, "Binary compatibility failed: custom != reference");
    };
}

#[macro_export]
macro_rules! assert_serialization_compatible {
    ($custom_type:expr, $reference_type:expr) => {
        let custom_bytes = bincode::serialize(&$custom_type).unwrap();
        let reference_bytes = bincode::serialize(&$reference_type).unwrap();
        assert_eq!(custom_bytes, reference_bytes, "Serialization compatibility failed");
    };
}

#[macro_export]
macro_rules! assert_deserialization_compatible {
    ($bytes:expr, $custom_type:ty, $reference_type:ty) => {
        let custom_result: Result<$custom_type, _> = bincode::deserialize($bytes);
        let reference_result: Result<$reference_type, _> = bincode::deserialize($bytes);

        match (custom_result, reference_result) {
            (Ok(custom), Ok(reference)) => {
                let custom_reserialized = bincode::serialize(&custom).unwrap();
                let reference_reserialized = bincode::serialize(&reference).unwrap();
                assert_eq!(custom_reserialized, reference_reserialized, "Deserialization compatibility failed");
            }
            (Err(_), Err(_)) => {}
            _ => panic!("Deserialization compatibility failed: one succeeded, one failed"),
        }
    };
}

#[macro_export]
macro_rules! assert_roundtrip_compatible {
    ($original:expr) => {
        let serialized = bincode::serialize(&$original).expect("Serialization failed");
        let deserialized = bincode::deserialize(&serialized).expect("Deserialization failed");
        assert_eq!($original, deserialized, "Roundtrip compatibility failed");
    };
}

#[macro_export]
macro_rules! assert_byte_array_equal {
    ($left:expr, $right:expr) => {
        if $left != $right {
            let diff_pos = $left.iter().zip($right.iter()).position(|(a, b)| a != b);
            match diff_pos {
                Some(pos) => panic!(
                    "Byte arrays differ at position {}: left=0x{:02x}, right=0x{:02x}",
                    pos, $left[pos], $right[pos]
                ),
                None => panic!("Byte arrays have different lengths: left={}, right={}", $left.len(), $right.len()),
            }
        }
    };
}

#[macro_export]
macro_rules! test_compatibility_suite {
    ($custom_type:ty, $reference_type:ty, $test_data:expr) => {
        for (i, data) in $test_data.iter().enumerate() {
            crate::common::compatibility_validators::validators::validate_100_percent_compatibility::<
                $custom_type,
                $reference_type,
            >(data)
            .unwrap_or_else(|e| panic!("Compatibility test {} failed: {:?}", i, e));
        }
    };
}

#[macro_export]
macro_rules! assert_structure_compatible {
    ($custom:expr, $reference:expr, $($field:ident),+) => {
        $(
            assert_eq!($custom.$field, $reference.$field,
                "Field '{}' mismatch: custom={:?}, reference={:?}",
                stringify!($field), $custom.$field, $reference.$field);
        )+
    };
}

#[macro_export]
macro_rules! assert_hash_compatible {
    ($custom_hash:expr, $reference_hash:expr) => {
        assert_eq!($custom_hash.as_ref(), $reference_hash.as_ref(), "Hash compatibility failed: custom != reference");
    };
}

#[macro_export]
macro_rules! assert_signature_compatible {
    ($custom_sig:expr, $reference_sig:expr) => {
        assert_eq!(
            $custom_sig.as_ref(),
            $reference_sig.as_ref(),
            "Signature compatibility failed: custom != reference"
        );
    };
}

#[macro_export]
macro_rules! assert_pubkey_compatible {
    ($custom_pubkey:expr, $reference_pubkey:expr) => {
        assert_eq!(
            $custom_pubkey.as_ref(),
            $reference_pubkey.as_ref(),
            "Pubkey compatibility failed: custom != reference"
        );
    };
}

#[allow(dead_code)]
pub fn assert_no_panic<F>(f: F, message: &str)
where
    F: FnOnce() + std::panic::UnwindSafe,
{
    let result = std::panic::catch_unwind(f);
    assert!(result.is_ok(), "{}", message);
}
