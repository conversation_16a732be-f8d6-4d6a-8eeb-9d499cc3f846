use bincode;
use solana_entry::entry::Entry as SolanaEntry;
use solana_message::{Message as SolanaMessage, VersionedMessage as SolanaVersionedMessage};
use solana_pubkey::Pubkey as SolanaPubkey;
use solana_transaction::versioned::VersionedTransaction as SolanaVersionedTransaction;

#[allow(dead_code)]
pub mod reference {
    use super::*;

    pub fn decode_entries_reference(bytes: &[u8]) -> Result<Vec<SolanaEntry>, bincode::Error> {
        bincode::deserialize(bytes)
    }

    pub fn serialize_entry_reference(entry: &SolanaEntry) -> Result<Vec<u8>, bincode::Error> {
        bincode::serialize(entry)
    }

    pub fn serialize_transaction_reference(tx: &SolanaVersionedTransaction) -> Result<Vec<u8>, bincode::Error> {
        bincode::serialize(tx)
    }

    pub fn serialize_message_reference(msg: &SolanaVersionedMessage) -> Result<Vec<u8>, bincode::Error> {
        bincode::serialize(msg)
    }

    pub fn serialize_pubkey_reference(pubkey: &SolanaPubkey) -> Result<Vec<u8>, bincode::Error> {
        bincode::serialize(pubkey)
    }

    pub fn deserialize_entry_reference(bytes: &[u8]) -> Result<SolanaEntry, bincode::Error> {
        bincode::deserialize(bytes)
    }

    pub fn deserialize_transaction_reference(bytes: &[u8]) -> Result<SolanaVersionedTransaction, bincode::Error> {
        bincode::deserialize(bytes)
    }

    pub fn deserialize_message_reference(bytes: &[u8]) -> Result<SolanaVersionedMessage, bincode::Error> {
        bincode::deserialize(bytes)
    }

    pub fn deserialize_pubkey_reference(bytes: &[u8]) -> Result<SolanaPubkey, bincode::Error> {
        bincode::deserialize(bytes)
    }

    pub fn create_sample_entry_reference() -> SolanaEntry {
        use solana_hash::Hash;

        SolanaEntry { num_hashes: 1, hash: Hash::default(), transactions: vec![] }
    }

    pub fn create_sample_transaction_reference() -> SolanaVersionedTransaction {
        use solana_signature::Signature;

        SolanaVersionedTransaction {
            signatures: vec![Signature::default()],
            message: SolanaVersionedMessage::Legacy(SolanaMessage::default()),
        }
    }

    pub fn create_sample_pubkey_reference() -> SolanaPubkey {
        SolanaPubkey::default()
    }

    pub fn validate_entry_hash_reference(entry: &SolanaEntry) -> bool {
        !entry.hash.as_ref().iter().all(|&b| b == 0)
    }

    pub fn get_entry_transaction_count_reference(entry: &SolanaEntry) -> usize {
        entry.transactions.len()
    }
}
