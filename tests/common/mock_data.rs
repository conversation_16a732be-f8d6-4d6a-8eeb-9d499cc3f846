use shredstream_decoder::types::{
    CompiledInstruction, Entry, LegacyMessage, MessageAddressTableLookup, MessageHeader, ParsedEntry, Pubkey,
    V0Message, VersionedMessage, VersionedTransaction,
};
use solana_hash::Hash;
use solana_signature::Signature;

#[allow(dead_code)]
pub mod generators {
    use super::*;

    pub fn generate_mock_entry_with_transactions(tx_count: usize) -> Entry {
        let mut transactions = Vec::new();

        for i in 0..tx_count {
            transactions.push(generate_mock_versioned_transaction_with_index(i));
        }

        Entry { num_hashes: 1, hash: generate_mock_hash_with_seed(42), transactions }
    }

    pub fn generate_mock_legacy_message() -> LegacyMessage {
        LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![generate_mock_pubkey_with_seed(1)],
            recent_blockhash: generate_mock_hash_with_seed(123),
            instructions: vec![generate_mock_compiled_instruction()],
        }
    }

    pub fn generate_mock_v0_message() -> V0Message {
        V0Message {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![generate_mock_pubkey_with_seed(2)],
            recent_blockhash: generate_mock_hash_with_seed(456),
            instructions: vec![generate_mock_compiled_instruction()],
            address_table_lookups: vec![generate_mock_address_table_lookup()],
        }
    }

    pub fn generate_mock_versioned_transaction() -> VersionedTransaction {
        VersionedTransaction {
            signatures: vec![generate_mock_signature_with_seed(789)],
            message: VersionedMessage::Legacy(generate_mock_legacy_message()),
        }
    }

    pub fn generate_mock_versioned_transaction_with_index(index: usize) -> VersionedTransaction {
        let seed = (index * 17 + 31) as u64;
        VersionedTransaction {
            signatures: vec![generate_mock_signature_with_seed(seed)],
            message: if index % 2 == 0 {
                VersionedMessage::Legacy(generate_mock_legacy_message_with_seed(seed))
            } else {
                VersionedMessage::V0(generate_mock_v0_message_with_seed(seed))
            },
        }
    }

    pub fn generate_mock_legacy_message_with_seed(seed: u64) -> LegacyMessage {
        LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![generate_mock_pubkey_with_seed(seed)],
            recent_blockhash: generate_mock_hash_with_seed(seed.wrapping_add(100)),
            instructions: vec![generate_mock_compiled_instruction_with_seed(seed.wrapping_add(200))],
        }
    }

    pub fn generate_mock_v0_message_with_seed(seed: u64) -> V0Message {
        V0Message {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![generate_mock_pubkey_with_seed(seed.wrapping_add(10))],
            recent_blockhash: generate_mock_hash_with_seed(seed.wrapping_add(110)),
            instructions: vec![generate_mock_compiled_instruction_with_seed(seed.wrapping_add(210))],
            address_table_lookups: vec![generate_mock_address_table_lookup_with_seed(seed.wrapping_add(300))],
        }
    }

    pub fn generate_empty_entry() -> Entry {
        Entry { num_hashes: 0, hash: Hash::default(), transactions: vec![] }
    }

    pub fn generate_max_size_entry() -> Entry {
        let mut transactions = Vec::new();
        for i in 0..100 {
            transactions.push(generate_mock_versioned_transaction_with_index(i));
        }

        Entry { num_hashes: u64::MAX, hash: generate_mock_hash_with_seed(u64::MAX), transactions }
    }

    pub fn generate_single_transaction_entry() -> Entry {
        Entry {
            num_hashes: 1,
            hash: generate_mock_hash_with_seed(999),
            transactions: vec![generate_mock_versioned_transaction()],
        }
    }

    pub fn generate_mock_compiled_instruction() -> CompiledInstruction {
        CompiledInstruction { program_id_index: 0, accounts: vec![0, 1], data: vec![1, 2, 3, 4] }
    }

    pub fn generate_mock_compiled_instruction_with_seed(seed: u64) -> CompiledInstruction {
        let data_len = (seed % 10 + 1) as usize;
        let mut data = Vec::new();
        for i in 0..data_len {
            data.push(((seed.wrapping_add(i as u64)) % 256) as u8);
        }

        CompiledInstruction {
            program_id_index: (seed % 256) as u8,
            accounts: vec![(seed % 256) as u8, ((seed.wrapping_add(1)) % 256) as u8],
            data,
        }
    }

    pub fn generate_mock_pubkey_with_seed(seed: u64) -> Pubkey {
        let mut bytes = [0u8; 32];
        let seed_bytes = seed.to_le_bytes();
        for i in 0..4 {
            bytes[i * 8..(i + 1) * 8].copy_from_slice(&seed_bytes);
        }
        Pubkey::new_from_array(bytes)
    }

    pub fn generate_mock_hash_with_seed(seed: u64) -> Hash {
        let mut bytes = [0u8; 32];
        let seed_bytes = seed.to_le_bytes();
        for i in 0..4 {
            bytes[i * 8..(i + 1) * 8].copy_from_slice(&seed_bytes);
        }
        Hash::new_from_array(bytes)
    }

    pub fn generate_mock_signature_with_seed(seed: u64) -> Signature {
        let mut bytes = [0u8; 64];
        let seed_bytes = seed.to_le_bytes();
        for i in 0..8 {
            bytes[i * 8..(i + 1) * 8].copy_from_slice(&seed_bytes);
        }
        Signature::from(bytes)
    }

    pub fn generate_mock_address_table_lookup() -> MessageAddressTableLookup {
        MessageAddressTableLookup {
            account_key: generate_mock_pubkey_with_seed(555),
            writable_indexes: vec![0, 1, 2],
            readonly_indexes: vec![3, 4, 5],
        }
    }

    pub fn generate_mock_address_table_lookup_with_seed(seed: u64) -> MessageAddressTableLookup {
        let writable_count = (seed % 5 + 1) as usize;
        let readonly_count = (seed % 3 + 1) as usize;

        let mut writable_indexes = Vec::new();
        for i in 0..writable_count {
            writable_indexes.push(((seed.wrapping_add(i as u64)) % 256) as u8);
        }

        let mut readonly_indexes = Vec::new();
        for i in 0..readonly_count {
            readonly_indexes.push(((seed.wrapping_add(100).wrapping_add(i as u64)) % 256) as u8);
        }

        MessageAddressTableLookup {
            account_key: generate_mock_pubkey_with_seed(seed.wrapping_add(1000)),
            writable_indexes,
            readonly_indexes,
        }
    }

    pub fn generate_malformed_data_samples() -> Vec<Vec<u8>> {
        vec![
            vec![],
            vec![0],
            vec![255],
            vec![1, 2, 3],
            vec![0; 10],
            vec![255; 10],
            vec![0; 100],
            vec![255; 100],
            vec![1, 0, 0, 0],
            vec![255, 255, 255, 255],
            generate_truncated_entry_bytes(),
            generate_oversized_entry_bytes(),
            generate_invalid_signature_count_bytes(),
            generate_invalid_message_version_bytes(),
        ]
    }

    fn generate_truncated_entry_bytes() -> Vec<u8> {
        let mut bytes = Vec::new();
        bytes.extend_from_slice(&1u64.to_le_bytes());
        bytes.extend_from_slice(&[1, 2, 3, 4]);
        bytes
    }

    fn generate_oversized_entry_bytes() -> Vec<u8> {
        let mut bytes = Vec::new();
        bytes.extend_from_slice(&1u64.to_le_bytes());
        bytes.extend_from_slice(&generate_mock_hash_with_seed(123).to_bytes());
        bytes.extend_from_slice(&u32::MAX.to_le_bytes());
        bytes.extend_from_slice(&vec![0; 1000]);
        bytes
    }

    fn generate_invalid_signature_count_bytes() -> Vec<u8> {
        let mut bytes = Vec::new();
        bytes.extend_from_slice(&u32::MAX.to_le_bytes());
        bytes.extend_from_slice(&[0; 64]);
        bytes
    }

    fn generate_invalid_message_version_bytes() -> Vec<u8> {
        vec![128, 0, 0, 0]
    }

    pub fn generate_edge_case_entries() -> Vec<Entry> {
        vec![
            generate_empty_entry(),
            generate_single_transaction_entry(),
            generate_max_size_entry(),
            Entry { num_hashes: 0, hash: Hash::default(), transactions: vec![generate_mock_versioned_transaction()] },
            Entry { num_hashes: 1000000, hash: generate_mock_hash_with_seed(12345), transactions: vec![] },
        ]
    }

    pub fn generate_various_message_types() -> Vec<VersionedMessage> {
        vec![
            VersionedMessage::Legacy(generate_mock_legacy_message()),
            VersionedMessage::V0(generate_mock_v0_message()),
            VersionedMessage::Legacy(LegacyMessage {
                header: MessageHeader {
                    num_required_signatures: 0,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 0,
                },
                account_keys: vec![],
                recent_blockhash: Hash::default(),
                instructions: vec![],
            }),
            VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 0,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 0,
                },
                account_keys: vec![],
                recent_blockhash: Hash::default(),
                instructions: vec![],
                address_table_lookups: vec![],
            }),
        ]
    }

    pub fn generate_mock_entry_with_index(index: u64) -> Entry {
        Entry {
            num_hashes: index,
            hash: generate_mock_hash_with_seed(index),
            transactions: vec![generate_mock_versioned_transaction_with_index(index as usize)],
        }
    }

    pub fn generate_mock_parsed_entry_with_entries(entry_count: usize) -> ParsedEntry {
        let mut entries = Vec::new();
        for i in 0..entry_count {
            entries.push(generate_mock_entry_with_index(i as u64));
        }

        ParsedEntry { slot: 12345, entries }
    }

    pub fn generate_mock_parsed_entry_with_index(index: u64) -> ParsedEntry {
        let entry_count = (index % 5 + 1) as usize; // 1-5 entries
        let mut entries = Vec::new();
        for i in 0..entry_count {
            entries.push(generate_mock_entry_with_index(index.wrapping_add(i as u64)));
        }

        ParsedEntry { slot: index, entries }
    }
}
