mod common;

use shredstream_decoder::types::{
    CompiledInstruction, LegacyMessage, MessageAddressTableLookup, MessageHeader, Pubkey, V0Message, VersionedMessage,
};
use solana_hash::Hash;

use common::mock_data::generators;

mod message_header_tests {
    use super::*;

    #[test]
    fn test_message_header_basic_structure() {
        let header = MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 2,
            num_readonly_unsigned_accounts: 3,
        };

        assert_eq!(header.num_required_signatures, 1);
        assert_eq!(header.num_readonly_signed_accounts, 2);
        assert_eq!(header.num_readonly_unsigned_accounts, 3);
    }

    #[test]
    fn test_message_header_default() {
        let header = MessageHeader::default();

        assert_eq!(header.num_required_signatures, 0);
        assert_eq!(header.num_readonly_signed_accounts, 0);
        assert_eq!(header.num_readonly_unsigned_accounts, 0);
    }

    #[test]
    fn test_message_header_clone_and_equality() {
        let header1 = MessageHeader {
            num_required_signatures: 5,
            num_readonly_signed_accounts: 10,
            num_readonly_unsigned_accounts: 15,
        };

        let header2 = header1.clone();
        assert_eq!(header1, header2);

        let header3 = MessageHeader {
            num_required_signatures: 5,
            num_readonly_signed_accounts: 10,
            num_readonly_unsigned_accounts: 16, // Different value
        };
        assert_ne!(header1, header3);
    }

    #[test]
    fn test_message_header_serialization_roundtrip() {
        let original = MessageHeader {
            num_required_signatures: 42,
            num_readonly_signed_accounts: 84,
            num_readonly_unsigned_accounts: 126,
        };

        let serialized = bincode::serialize(&original).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();

        assert_eq!(original, deserialized);
    }

    #[test]
    fn test_message_header_edge_cases() {
        // Test with maximum u8 values
        let max_header = MessageHeader {
            num_required_signatures: u8::MAX,
            num_readonly_signed_accounts: u8::MAX,
            num_readonly_unsigned_accounts: u8::MAX,
        };

        let serialized = bincode::serialize(&max_header).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
        assert_eq!(max_header, deserialized);

        // Test with zero values
        let zero_header = MessageHeader {
            num_required_signatures: 0,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 0,
        };

        let serialized = bincode::serialize(&zero_header).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
        assert_eq!(zero_header, deserialized);
    }

    #[test]
    fn test_message_header_with_mock_generators() {
        // Test with mock data generators
        let mock_legacy = generators::generate_mock_legacy_message();
        let header = &mock_legacy.header;

        // Verify header is valid
        assert!(header.num_required_signatures <= u8::MAX);
        assert!(header.num_readonly_signed_accounts <= u8::MAX);
        assert!(header.num_readonly_unsigned_accounts <= u8::MAX);

        // Test serialization of mock data
        let serialized = bincode::serialize(header).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
        assert_eq!(*header, deserialized);
    }

    #[test]
    fn test_message_header_binary_layout() {
        // Test that MessageHeader has expected binary layout (3 bytes)
        let header = MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 2,
            num_readonly_unsigned_accounts: 3,
        };

        let serialized = bincode::serialize(&header).unwrap();

        // MessageHeader should serialize to exactly 3 bytes (3 u8 fields)
        assert_eq!(serialized.len(), 3);
        assert_eq!(serialized[0], 1); // num_required_signatures
        assert_eq!(serialized[1], 2); // num_readonly_signed_accounts
        assert_eq!(serialized[2], 3); // num_readonly_unsigned_accounts
    }

    #[test]
    fn test_message_header_signature_counts() {
        // Test various signature count scenarios
        let scenarios = vec![
            (0, 0, 0),       // No signatures
            (1, 0, 0),       // Single signature
            (5, 2, 3),       // Multiple signatures with readonly accounts
            (255, 128, 127), // High values
        ];

        for (req_sigs, readonly_signed, readonly_unsigned) in scenarios {
            let header = MessageHeader {
                num_required_signatures: req_sigs,
                num_readonly_signed_accounts: readonly_signed,
                num_readonly_unsigned_accounts: readonly_unsigned,
            };

            // Test serialization roundtrip
            let serialized = bincode::serialize(&header).unwrap();
            let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
            assert_eq!(header, deserialized);

            // Verify field values
            assert_eq!(header.num_required_signatures, req_sigs);
            assert_eq!(header.num_readonly_signed_accounts, readonly_signed);
            assert_eq!(header.num_readonly_unsigned_accounts, readonly_unsigned);
        }
    }

    #[test]
    fn test_message_header_readonly_accounts() {
        // Test readonly account counting logic
        let header = MessageHeader {
            num_required_signatures: 3,
            num_readonly_signed_accounts: 1,
            num_readonly_unsigned_accounts: 2,
        };

        // Total readonly accounts = readonly_signed + readonly_unsigned
        let total_readonly = header.num_readonly_signed_accounts + header.num_readonly_unsigned_accounts;
        assert_eq!(total_readonly, 3);

        // Verify that readonly_signed <= num_required_signatures (logical constraint)
        assert!(header.num_readonly_signed_accounts <= header.num_required_signatures);
    }

    #[test]
    fn test_message_header_comprehensive_roundtrip() {
        // Test with various combinations to ensure comprehensive coverage
        for req_sigs in [0, 1, 5, 10, 255] {
            for readonly_signed in [0, 1, req_sigs.min(5)] {
                for readonly_unsigned in [0, 1, 5, 10, 255] {
                    let header = MessageHeader {
                        num_required_signatures: req_sigs,
                        num_readonly_signed_accounts: readonly_signed,
                        num_readonly_unsigned_accounts: readonly_unsigned,
                    };

                    // Test serialization roundtrip
                    let serialized = bincode::serialize(&header).unwrap();
                    let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
                    assert_eq!(header, deserialized);

                    // Verify binary layout consistency
                    assert_eq!(serialized.len(), 3);
                    assert_eq!(serialized[0], req_sigs);
                    assert_eq!(serialized[1], readonly_signed);
                    assert_eq!(serialized[2], readonly_unsigned);
                }
            }
        }
    }
}

mod legacy_message_tests {
    use super::*;
    use solana_hash::Hash;

    #[test]
    fn test_legacy_message_basic_structure() {
        let header = MessageHeader {
            num_required_signatures: 2,
            num_readonly_signed_accounts: 1,
            num_readonly_unsigned_accounts: 3,
        };
        let account_keys = vec![Pubkey::new_from_array([1u8; 32]), Pubkey::new_from_array([2u8; 32])];
        let recent_blockhash = Hash::new_from_array([42u8; 32]);
        let instructions =
            vec![CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![10, 20, 30] }];

        let message = LegacyMessage {
            header: header.clone(),
            account_keys: account_keys.clone(),
            recent_blockhash,
            instructions: instructions.clone(),
        };

        assert_eq!(message.header, header);
        assert_eq!(message.account_keys, account_keys);
        assert_eq!(message.recent_blockhash, recent_blockhash);
        assert_eq!(message.instructions, instructions);
    }

    #[test]
    fn test_legacy_message_default() {
        let message = LegacyMessage::default();

        assert_eq!(message.header, MessageHeader::default());
        assert_eq!(message.account_keys, Vec::<Pubkey>::new());
        assert_eq!(message.recent_blockhash, Hash::default());
        assert_eq!(message.instructions, Vec::<CompiledInstruction>::new());
    }

    #[test]
    fn test_legacy_message_clone_and_equality() {
        let message1 = LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![Pubkey::new_from_array([100u8; 32])],
            recent_blockhash: Hash::new_from_array([200u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![0], data: vec![255] }],
        };

        let message2 = message1.clone();
        assert_eq!(message1, message2);

        let message3 = LegacyMessage {
            header: message1.header.clone(),
            account_keys: message1.account_keys.clone(),
            recent_blockhash: Hash::new_from_array([201u8; 32]), // Different blockhash
            instructions: message1.instructions.clone(),
        };
        assert_ne!(message1, message3);
    }

    #[test]
    fn test_legacy_message_serialization_roundtrip() {
        let original = LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 3,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![
                Pubkey::new_from_array([10u8; 32]),
                Pubkey::new_from_array([20u8; 32]),
                Pubkey::new_from_array([30u8; 32]),
            ],
            recent_blockhash: Hash::new_from_array([123u8; 32]),
            instructions: vec![
                CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![100, 101, 102] },
                CompiledInstruction { program_id_index: 1, accounts: vec![0, 2], data: vec![200, 201] },
            ],
        };

        let serialized = bincode::serialize(&original).unwrap();
        let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();

        assert_eq!(original, deserialized);
        assert_eq!(original.header, deserialized.header);
        assert_eq!(original.account_keys, deserialized.account_keys);
        assert_eq!(original.recent_blockhash, deserialized.recent_blockhash);
        assert_eq!(original.instructions, deserialized.instructions);
    }

    #[test]
    fn test_legacy_message_empty_collections() {
        let message = LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 0,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![],
            recent_blockhash: Hash::new_from_array([0u8; 32]),
            instructions: vec![],
        };

        let serialized = bincode::serialize(&message).unwrap();
        let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(message, deserialized);

        // Verify empty collections
        assert!(message.account_keys.is_empty());
        assert!(message.instructions.is_empty());
    }

    #[test]
    fn test_legacy_message_large_collections() {
        // Test with larger collections
        let account_keys: Vec<Pubkey> = (0..50).map(|i| Pubkey::new_from_array([i as u8; 32])).collect();

        let instructions: Vec<CompiledInstruction> = (0..20)
            .map(|i| CompiledInstruction {
                program_id_index: i as u8,
                accounts: vec![i as u8, (i + 1) as u8],
                data: vec![i as u8, (i * 2) as u8, (i * 3) as u8],
            })
            .collect();

        let message = LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 10,
                num_readonly_signed_accounts: 5,
                num_readonly_unsigned_accounts: 15,
            },
            account_keys: account_keys.clone(),
            recent_blockhash: Hash::new_from_array([255u8; 32]),
            instructions: instructions.clone(),
        };

        let serialized = bincode::serialize(&message).unwrap();
        let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();

        assert_eq!(message, deserialized);
        assert_eq!(message.account_keys.len(), 50);
        assert_eq!(message.instructions.len(), 20);
        assert_eq!(deserialized.account_keys, account_keys);
        assert_eq!(deserialized.instructions, instructions);
    }

    #[test]
    fn test_legacy_message_with_mock_generators() {
        // Test with mock data generators
        let mock_message = generators::generate_mock_legacy_message();

        // Verify structure is valid
        assert_ne!(mock_message.header.num_required_signatures, 0);
        assert!(!mock_message.account_keys.is_empty());
        assert_ne!(mock_message.recent_blockhash, Hash::default());
        assert!(!mock_message.instructions.is_empty());

        // Test serialization of mock data
        let serialized = bincode::serialize(&mock_message).unwrap();
        let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(mock_message, deserialized);
    }

    #[test]
    fn test_legacy_message_with_seed_generators() {
        let test_seeds = vec![1u64, 100, 1000, 12345, u64::MAX];

        for seed in test_seeds {
            let message = generators::generate_mock_legacy_message_with_seed(seed);

            // Verify generated data is deterministic
            let message2 = generators::generate_mock_legacy_message_with_seed(seed);
            assert_eq!(message, message2);

            // Test serialization roundtrip
            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(message, deserialized);

            // Verify all components are properly generated
            assert!(!message.account_keys.is_empty());
            assert!(!message.instructions.is_empty());
            assert_ne!(message.recent_blockhash, Hash::default());
        }
    }

    #[test]
    fn test_legacy_message_header_compatibility() {
        // Test that header fields are properly integrated
        let header = MessageHeader {
            num_required_signatures: 5,
            num_readonly_signed_accounts: 2,
            num_readonly_unsigned_accounts: 3,
        };

        let message = LegacyMessage {
            header: header.clone(),
            account_keys: vec![Pubkey::new_from_array([1u8; 32]); 10],
            recent_blockhash: Hash::new_from_array([50u8; 32]),
            instructions: vec![CompiledInstruction::default()],
        };

        // Verify header integration
        assert_eq!(message.header.num_required_signatures, 5);
        assert_eq!(message.header.num_readonly_signed_accounts, 2);
        assert_eq!(message.header.num_readonly_unsigned_accounts, 3);

        // Test serialization preserves header
        let serialized = bincode::serialize(&message).unwrap();
        let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(message.header, deserialized.header);
    }

    #[test]
    fn test_legacy_message_account_keys_compatibility() {
        // Test various account key scenarios
        let scenarios = vec![
            vec![],                                                                    // Empty
            vec![Pubkey::default()],                                                   // Single default
            vec![Pubkey::new_from_array([255u8; 32])],                                 // Single max
            (0..10).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(),          // Multiple
            (0..100).map(|i| Pubkey::new_from_array([(i % 256) as u8; 32])).collect(), // Large
        ];

        for account_keys in scenarios {
            let message = LegacyMessage {
                header: MessageHeader::default(),
                account_keys: account_keys.clone(),
                recent_blockhash: Hash::new_from_array([42u8; 32]),
                instructions: vec![],
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.account_keys, account_keys);
            assert_eq!(deserialized.account_keys, account_keys);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_legacy_message_recent_blockhash_compatibility() {
        // Test various blockhash scenarios
        let blockhashes = vec![
            Hash::default(),                   // Default (all zeros)
            Hash::new_from_array([255u8; 32]), // All max values
            Hash::new_from_array([128u8; 32]), // Mid values
            Hash::new_from_array([
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
                29, 30, 31, 32,
            ]), // Sequential
        ];

        for blockhash in blockhashes {
            let message = LegacyMessage {
                header: MessageHeader::default(),
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: blockhash,
                instructions: vec![],
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.recent_blockhash, blockhash);
            assert_eq!(deserialized.recent_blockhash, blockhash);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_legacy_message_instructions_compatibility() {
        // Test various instruction scenarios
        let scenarios = vec![
            vec![],                               // Empty
            vec![CompiledInstruction::default()], // Single default
            vec![CompiledInstruction {
                program_id_index: 255,
                accounts: vec![0, 1, 2, 3, 4],
                data: vec![255, 254, 253],
            }], // Single complex
            (0..10)
                .map(|i| CompiledInstruction {
                    program_id_index: i as u8,
                    accounts: vec![i as u8, (i + 1) as u8],
                    data: vec![i as u8],
                })
                .collect(), // Multiple
            (0..50)
                .map(|i| CompiledInstruction {
                    program_id_index: (i % 256) as u8,
                    accounts: (0..10).map(|j| ((i + j) % 256) as u8).collect(),
                    data: (0..20).map(|k| ((i + k) % 256) as u8).collect(),
                })
                .collect(), // Large complex
        ];

        for instructions in scenarios {
            let message = LegacyMessage {
                header: MessageHeader::default(),
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: Hash::new_from_array([42u8; 32]),
                instructions: instructions.clone(),
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.instructions, instructions);
            assert_eq!(deserialized.instructions, instructions);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_legacy_message_edge_cases() {
        // Test comprehensive edge case combinations
        let edge_cases = vec![
            // (header, account_keys_count, blockhash_pattern, instructions_count)
            (MessageHeader::default(), 0, [0u8; 32], 0), // All minimal
            (
                MessageHeader {
                    num_required_signatures: 255,
                    num_readonly_signed_accounts: 255,
                    num_readonly_unsigned_accounts: 255,
                },
                255,
                [255u8; 32],
                255,
            ), // All maximal (within reasonable limits)
            (
                MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 0,
                },
                1,
                [42u8; 32],
                1,
            ), // Minimal valid transaction
            (
                MessageHeader {
                    num_required_signatures: 10,
                    num_readonly_signed_accounts: 5,
                    num_readonly_unsigned_accounts: 15,
                },
                30,
                [128u8; 32],
                20,
            ), // Complex realistic scenario
        ];

        for (header, account_count, blockhash_pattern, instruction_count) in edge_cases {
            let account_keys: Vec<Pubkey> =
                (0..account_count).map(|i| Pubkey::new_from_array([(i % 256) as u8; 32])).collect();

            let instructions: Vec<CompiledInstruction> = (0..instruction_count)
                .map(|i| CompiledInstruction {
                    program_id_index: (i % 256) as u8,
                    accounts: vec![(i % 256) as u8],
                    data: vec![(i % 256) as u8, ((i + 1) % 256) as u8],
                })
                .collect();

            let message = LegacyMessage {
                header: header.clone(),
                account_keys: account_keys.clone(),
                recent_blockhash: Hash::new_from_array(blockhash_pattern),
                instructions: instructions.clone(),
            };

            // Test serialization roundtrip
            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(message, deserialized);

            // Verify all fields are preserved
            assert_eq!(message.header, header);
            assert_eq!(message.account_keys, account_keys);
            assert_eq!(message.recent_blockhash.to_bytes(), blockhash_pattern);
            assert_eq!(message.instructions, instructions);
        }
    }

    #[test]
    fn test_legacy_message_comprehensive_roundtrip() {
        // Test comprehensive scenarios with different combinations
        let test_cases = vec![
            // Basic cases
            (MessageHeader::default(), vec![], Hash::default(), vec![]),
            (
                MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 0,
                },
                vec![Pubkey::new_from_array([1u8; 32])],
                Hash::new_from_array([1u8; 32]),
                vec![CompiledInstruction::default()],
            ),
            // Generated cases with seeds
            (
                MessageHeader {
                    num_required_signatures: 2,
                    num_readonly_signed_accounts: 1,
                    num_readonly_unsigned_accounts: 1,
                },
                vec![generators::generate_mock_pubkey_with_seed(123), generators::generate_mock_pubkey_with_seed(456)],
                generators::generate_mock_hash_with_seed(789),
                vec![
                    generators::generate_mock_compiled_instruction_with_seed(111),
                    generators::generate_mock_compiled_instruction_with_seed(222),
                ],
            ),
            // Complex realistic case
            (
                MessageHeader {
                    num_required_signatures: 5,
                    num_readonly_signed_accounts: 2,
                    num_readonly_unsigned_accounts: 3,
                },
                (0..10).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(),
                Hash::new_from_array([200u8; 32]),
                (0..5)
                    .map(|i| CompiledInstruction {
                        program_id_index: i as u8,
                        accounts: vec![i as u8, (i + 1) as u8, (i + 2) as u8],
                        data: vec![i as u8, (i * 2) as u8, (i * 3) as u8, (i * 4) as u8],
                    })
                    .collect(),
            ),
        ];

        for (header, account_keys, recent_blockhash, instructions) in test_cases {
            let original = LegacyMessage {
                header,
                account_keys: account_keys.clone(),
                recent_blockhash,
                instructions: instructions.clone(),
            };

            // First roundtrip
            let serialized = bincode::serialize(&original).unwrap();
            let deserialized: LegacyMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(original, deserialized);

            // Second roundtrip to ensure stability
            let re_serialized = bincode::serialize(&deserialized).unwrap();
            let re_deserialized: LegacyMessage = bincode::deserialize(&re_serialized).unwrap();
            assert_eq!(original, re_deserialized);
            assert_eq!(serialized, re_serialized);

            // Verify individual fields
            assert_eq!(original.header, re_deserialized.header);
            assert_eq!(original.account_keys, re_deserialized.account_keys);
            assert_eq!(original.recent_blockhash, re_deserialized.recent_blockhash);
            assert_eq!(original.instructions, re_deserialized.instructions);
        }
    }
}

mod v0_message_tests {
    use super::*;

    #[test]
    fn test_v0_message_basic_structure() {
        let message = V0Message {
            header: MessageHeader {
                num_required_signatures: 2,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 3,
            },
            account_keys: vec![Pubkey::new_from_array([10u8; 32]), Pubkey::new_from_array([20u8; 32])],
            recent_blockhash: Hash::new_from_array([42u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![100, 101] }],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([99u8; 32]),
                writable_indexes: vec![0, 1],
                readonly_indexes: vec![2, 3],
            }],
        };

        // Verify all fields are accessible and correct
        assert_eq!(message.header.num_required_signatures, 2);
        assert_eq!(message.header.num_readonly_signed_accounts, 1);
        assert_eq!(message.header.num_readonly_unsigned_accounts, 3);
        assert_eq!(message.account_keys.len(), 2);
        assert_eq!(message.account_keys[0], Pubkey::new_from_array([10u8; 32]));
        assert_eq!(message.account_keys[1], Pubkey::new_from_array([20u8; 32]));
        assert_eq!(message.recent_blockhash, Hash::new_from_array([42u8; 32]));
        assert_eq!(message.instructions.len(), 1);
        assert_eq!(message.instructions[0].program_id_index, 0);
        assert_eq!(message.instructions[0].accounts, vec![1]);
        assert_eq!(message.instructions[0].data, vec![100, 101]);
        assert_eq!(message.address_table_lookups.len(), 1);
        assert_eq!(message.address_table_lookups[0].account_key, Pubkey::new_from_array([99u8; 32]));
        assert_eq!(message.address_table_lookups[0].writable_indexes, vec![0, 1]);
        assert_eq!(message.address_table_lookups[0].readonly_indexes, vec![2, 3]);
    }

    #[test]
    fn test_v0_message_default() {
        let message = V0Message::default();

        // Verify default values
        assert_eq!(message.header, MessageHeader::default());
        assert!(message.account_keys.is_empty());
        assert_eq!(message.recent_blockhash, Hash::default());
        assert!(message.instructions.is_empty());
        assert!(message.address_table_lookups.is_empty());
    }

    #[test]
    fn test_v0_message_clone_and_equality() {
        let original = V0Message {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![Pubkey::new_from_array([5u8; 32])],
            recent_blockhash: Hash::new_from_array([123u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![], data: vec![42] }],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([77u8; 32]),
                writable_indexes: vec![],
                readonly_indexes: vec![0],
            }],
        };

        let cloned = original.clone();
        assert_eq!(original, cloned);

        // Verify deep equality
        assert_eq!(original.header, cloned.header);
        assert_eq!(original.account_keys, cloned.account_keys);
        assert_eq!(original.recent_blockhash, cloned.recent_blockhash);
        assert_eq!(original.instructions, cloned.instructions);
        assert_eq!(original.address_table_lookups, cloned.address_table_lookups);
    }

    #[test]
    fn test_v0_message_serialization_roundtrip() {
        let original = V0Message {
            header: MessageHeader {
                num_required_signatures: 3,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![
                Pubkey::new_from_array([10u8; 32]),
                Pubkey::new_from_array([20u8; 32]),
                Pubkey::new_from_array([30u8; 32]),
            ],
            recent_blockhash: Hash::new_from_array([123u8; 32]),
            instructions: vec![
                CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![100, 101, 102] },
                CompiledInstruction { program_id_index: 1, accounts: vec![0, 2], data: vec![200, 201] },
            ],
            address_table_lookups: vec![
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([99u8; 32]),
                    writable_indexes: vec![0, 1],
                    readonly_indexes: vec![2, 3, 4],
                },
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([88u8; 32]),
                    writable_indexes: vec![],
                    readonly_indexes: vec![5],
                },
            ],
        };

        let serialized = bincode::serialize(&original).unwrap();
        let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

        assert_eq!(original, deserialized);
    }

    #[test]
    fn test_v0_message_empty_collections() {
        let message = V0Message {
            header: MessageHeader {
                num_required_signatures: 0,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![],
            recent_blockhash: Hash::new_from_array([0u8; 32]),
            instructions: vec![],
            address_table_lookups: vec![],
        };

        let serialized = bincode::serialize(&message).unwrap();
        let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();
        assert_eq!(message, deserialized);

        // Verify empty collections
        assert!(message.account_keys.is_empty());
        assert!(message.instructions.is_empty());
        assert!(message.address_table_lookups.is_empty());
    }

    #[test]
    fn test_v0_message_large_collections() {
        // Create message with large collections to test short_vec serialization
        let mut account_keys = Vec::new();
        for i in 0..50 {
            account_keys.push(Pubkey::new_from_array([i as u8; 32]));
        }

        let mut instructions = Vec::new();
        for i in 0..20 {
            instructions.push(CompiledInstruction {
                program_id_index: (i % 256) as u8,
                accounts: vec![(i % 256) as u8, ((i + 1) % 256) as u8],
                data: vec![(i % 256) as u8, ((i + 10) % 256) as u8, ((i + 20) % 256) as u8],
            });
        }

        let mut address_table_lookups = Vec::new();
        for i in 0..15 {
            address_table_lookups.push(MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([(i + 100) as u8; 32]),
                writable_indexes: vec![(i % 256) as u8, ((i + 1) % 256) as u8],
                readonly_indexes: vec![((i + 2) % 256) as u8, ((i + 3) % 256) as u8, ((i + 4) % 256) as u8],
            });
        }

        let message = V0Message {
            header: MessageHeader {
                num_required_signatures: 5,
                num_readonly_signed_accounts: 2,
                num_readonly_unsigned_accounts: 10,
            },
            account_keys,
            recent_blockhash: Hash::new_from_array([255u8; 32]),
            instructions,
            address_table_lookups,
        };

        let serialized = bincode::serialize(&message).unwrap();
        let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

        assert_eq!(message, deserialized);
        assert_eq!(message.account_keys.len(), 50);
        assert_eq!(message.instructions.len(), 20);
        assert_eq!(message.address_table_lookups.len(), 15);
    }

    #[test]
    fn test_v0_message_with_mock_generators() {
        let message = generators::generate_mock_v0_message();

        // Verify structure is valid
        assert_eq!(message.header.num_required_signatures, 1);
        assert_eq!(message.header.num_readonly_signed_accounts, 0);
        assert_eq!(message.header.num_readonly_unsigned_accounts, 1);
        assert_eq!(message.account_keys.len(), 1);
        assert_eq!(message.instructions.len(), 1);
        assert_eq!(message.address_table_lookups.len(), 1);

        // Test serialization roundtrip
        let serialized = bincode::serialize(&message).unwrap();
        let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();
        assert_eq!(message, deserialized);
    }

    #[test]
    fn test_v0_message_with_seed_generators() {
        let seeds = [100, 200, 300, 400, 500];

        for seed in seeds {
            let message = generators::generate_mock_v0_message_with_seed(seed);

            // Verify structure is valid
            assert_eq!(message.header.num_required_signatures, 1);
            assert_eq!(message.header.num_readonly_signed_accounts, 0);
            assert_eq!(message.header.num_readonly_unsigned_accounts, 1);
            assert_eq!(message.account_keys.len(), 1);
            assert_eq!(message.instructions.len(), 1);
            assert_eq!(message.address_table_lookups.len(), 1);

            // Test serialization roundtrip
            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_v0_message_header_compatibility() {
        let headers = vec![
            MessageHeader {
                num_required_signatures: 0,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            MessageHeader {
                num_required_signatures: 5,
                num_readonly_signed_accounts: 2,
                num_readonly_unsigned_accounts: 10,
            },
            MessageHeader {
                num_required_signatures: 255,
                num_readonly_signed_accounts: 255,
                num_readonly_unsigned_accounts: 255,
            },
        ];

        for header in headers {
            let message = V0Message {
                header: header.clone(),
                account_keys: vec![Pubkey::new_from_array([42u8; 32])],
                recent_blockhash: Hash::new_from_array([123u8; 32]),
                instructions: vec![],
                address_table_lookups: vec![],
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.header, header);
            assert_eq!(deserialized.header, header);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_v0_message_account_keys_compatibility() {
        let scenarios = vec![
            vec![],                                  // Empty
            vec![Pubkey::new_from_array([1u8; 32])], // Single
            vec![
                Pubkey::new_from_array([1u8; 32]),
                Pubkey::new_from_array([2u8; 32]),
                Pubkey::new_from_array([3u8; 32]),
            ], // Multiple
            (0..25).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(), // Large collection
        ];

        for account_keys in scenarios {
            let message = V0Message {
                header: MessageHeader::default(),
                account_keys: account_keys.clone(),
                recent_blockhash: Hash::new_from_array([42u8; 32]),
                instructions: vec![],
                address_table_lookups: vec![],
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.account_keys, account_keys);
            assert_eq!(deserialized.account_keys, account_keys);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_v0_message_recent_blockhash_compatibility() {
        let blockhashes = vec![
            Hash::default(),
            Hash::new_from_array([0u8; 32]),
            Hash::new_from_array([255u8; 32]),
            Hash::new_from_array([42u8; 32]),
        ];

        for blockhash in blockhashes {
            let message = V0Message {
                header: MessageHeader::default(),
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: blockhash,
                instructions: vec![],
                address_table_lookups: vec![],
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.recent_blockhash, blockhash);
            assert_eq!(deserialized.recent_blockhash, blockhash);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_v0_message_instructions_compatibility() {
        let scenarios = vec![
            vec![],                                                                                      // Empty
            vec![CompiledInstruction { program_id_index: 0, accounts: vec![], data: vec![] }],           // Single empty
            vec![CompiledInstruction { program_id_index: 1, accounts: vec![0, 1], data: vec![42, 43] }], // Single with data
            vec![
                CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![100] },
                CompiledInstruction { program_id_index: 1, accounts: vec![0, 2], data: vec![200, 201] },
                CompiledInstruction { program_id_index: 2, accounts: vec![], data: vec![] },
            ], // Multiple mixed
        ];

        for instructions in scenarios {
            let message = V0Message {
                header: MessageHeader::default(),
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: Hash::new_from_array([42u8; 32]),
                instructions: instructions.clone(),
                address_table_lookups: vec![],
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.instructions, instructions);
            assert_eq!(deserialized.instructions, instructions);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_v0_message_address_table_lookups_compatibility() {
        let scenarios = vec![
            vec![], // Empty
            vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([1u8; 32]),
                writable_indexes: vec![],
                readonly_indexes: vec![],
            }], // Single empty
            vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([2u8; 32]),
                writable_indexes: vec![0, 1],
                readonly_indexes: vec![2, 3, 4],
            }], // Single with data
            vec![
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([10u8; 32]),
                    writable_indexes: vec![0],
                    readonly_indexes: vec![1, 2],
                },
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([20u8; 32]),
                    writable_indexes: vec![],
                    readonly_indexes: vec![],
                },
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([30u8; 32]),
                    writable_indexes: vec![5, 6, 7],
                    readonly_indexes: vec![8],
                },
            ], // Multiple mixed
        ];

        for address_table_lookups in scenarios {
            let message = V0Message {
                header: MessageHeader::default(),
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: Hash::new_from_array([42u8; 32]),
                instructions: vec![],
                address_table_lookups: address_table_lookups.clone(),
            };

            let serialized = bincode::serialize(&message).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();

            assert_eq!(message.address_table_lookups, address_table_lookups);
            assert_eq!(deserialized.address_table_lookups, address_table_lookups);
            assert_eq!(message, deserialized);
        }
    }

    #[test]
    fn test_v0_message_edge_cases() {
        // Test with maximum values
        let max_message = V0Message {
            header: MessageHeader {
                num_required_signatures: 255,
                num_readonly_signed_accounts: 255,
                num_readonly_unsigned_accounts: 255,
            },
            account_keys: vec![Pubkey::new_from_array([255u8; 32])],
            recent_blockhash: Hash::new_from_array([255u8; 32]),
            instructions: vec![CompiledInstruction {
                program_id_index: 255,
                accounts: vec![255, 254, 253],
                data: vec![255, 254, 253, 252],
            }],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([255u8; 32]),
                writable_indexes: vec![255, 254],
                readonly_indexes: vec![253, 252, 251],
            }],
        };

        let serialized = bincode::serialize(&max_message).unwrap();
        let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();
        assert_eq!(max_message, deserialized);

        // Test with zero values
        let zero_message = V0Message {
            header: MessageHeader {
                num_required_signatures: 0,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![Pubkey::new_from_array([0u8; 32])],
            recent_blockhash: Hash::new_from_array([0u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![0], data: vec![0] }],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([0u8; 32]),
                writable_indexes: vec![0],
                readonly_indexes: vec![0],
            }],
        };

        let serialized = bincode::serialize(&zero_message).unwrap();
        let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();
        assert_eq!(zero_message, deserialized);
    }

    #[test]
    fn test_v0_message_comprehensive_roundtrip() {
        // Test multiple scenarios with comprehensive data
        let test_cases = vec![
            // Case 1: Minimal message
            V0Message {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 0,
                },
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: Hash::new_from_array([1u8; 32]),
                instructions: vec![],
                address_table_lookups: vec![],
            },
            // Case 2: Complex message with all fields populated
            V0Message {
                header: MessageHeader {
                    num_required_signatures: 3,
                    num_readonly_signed_accounts: 1,
                    num_readonly_unsigned_accounts: 2,
                },
                account_keys: vec![
                    Pubkey::new_from_array([10u8; 32]),
                    Pubkey::new_from_array([20u8; 32]),
                    Pubkey::new_from_array([30u8; 32]),
                    Pubkey::new_from_array([40u8; 32]),
                ],
                recent_blockhash: Hash::new_from_array([123u8; 32]),
                instructions: vec![
                    CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![100, 101, 102] },
                    CompiledInstruction { program_id_index: 1, accounts: vec![0, 3], data: vec![200] },
                    CompiledInstruction { program_id_index: 2, accounts: vec![], data: vec![] },
                ],
                address_table_lookups: vec![
                    MessageAddressTableLookup {
                        account_key: Pubkey::new_from_array([99u8; 32]),
                        writable_indexes: vec![0, 1, 2],
                        readonly_indexes: vec![3, 4, 5, 6],
                    },
                    MessageAddressTableLookup {
                        account_key: Pubkey::new_from_array([88u8; 32]),
                        writable_indexes: vec![],
                        readonly_indexes: vec![7],
                    },
                ],
            },
            // Case 3: Large collections
            V0Message {
                header: MessageHeader {
                    num_required_signatures: 10,
                    num_readonly_signed_accounts: 5,
                    num_readonly_unsigned_accounts: 15,
                },
                account_keys: (0..30).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(),
                recent_blockhash: Hash::new_from_array([200u8; 32]),
                instructions: (0..10)
                    .map(|i| CompiledInstruction {
                        program_id_index: (i % 256) as u8,
                        accounts: vec![(i % 256) as u8, ((i + 1) % 256) as u8],
                        data: vec![(i % 256) as u8, ((i + 10) % 256) as u8, ((i + 20) % 256) as u8],
                    })
                    .collect(),
                address_table_lookups: (0..8)
                    .map(|i| MessageAddressTableLookup {
                        account_key: Pubkey::new_from_array([(i + 50) as u8; 32]),
                        writable_indexes: vec![(i % 256) as u8, ((i + 1) % 256) as u8],
                        readonly_indexes: vec![((i + 2) % 256) as u8, ((i + 3) % 256) as u8],
                    })
                    .collect(),
            },
        ];

        for (i, original) in test_cases.iter().enumerate() {
            // First roundtrip
            let serialized = bincode::serialize(original).unwrap();
            let deserialized: V0Message = bincode::deserialize(&serialized).unwrap();
            assert_eq!(*original, deserialized, "First roundtrip failed for case {}", i);

            // Second roundtrip to ensure stability
            let re_serialized = bincode::serialize(&deserialized).unwrap();
            let re_deserialized: V0Message = bincode::deserialize(&re_serialized).unwrap();
            assert_eq!(*original, re_deserialized, "Second roundtrip failed for case {}", i);
            assert_eq!(serialized, re_serialized, "Serialization not stable for case {}", i);

            // Verify individual fields
            assert_eq!(original.header, re_deserialized.header, "Header mismatch for case {}", i);
            assert_eq!(original.account_keys, re_deserialized.account_keys, "Account keys mismatch for case {}", i);
            assert_eq!(
                original.recent_blockhash, re_deserialized.recent_blockhash,
                "Recent blockhash mismatch for case {}",
                i
            );
            assert_eq!(original.instructions, re_deserialized.instructions, "Instructions mismatch for case {}", i);
            assert_eq!(
                original.address_table_lookups, re_deserialized.address_table_lookups,
                "Address table lookups mismatch for case {}",
                i
            );
        }
    }
}

mod versioned_message_tests {
    use super::*;

    #[test]
    fn test_versioned_message_basic_structure() {
        // Test Legacy variant
        let legacy_message = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 2,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 3,
            },
            account_keys: vec![Pubkey::new_from_array([10u8; 32]), Pubkey::new_from_array([20u8; 32])],
            recent_blockhash: Hash::new_from_array([42u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![100, 101] }],
        });

        // Test V0 variant
        let v0_message = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![Pubkey::new_from_array([30u8; 32])],
            recent_blockhash: Hash::new_from_array([123u8; 32]),
            instructions: vec![],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([99u8; 32]),
                writable_indexes: vec![0, 1],
                readonly_indexes: vec![2, 3],
            }],
        });

        // Verify variants are accessible
        match legacy_message {
            VersionedMessage::Legacy(ref msg) => {
                assert_eq!(msg.header.num_required_signatures, 2);
                assert_eq!(msg.account_keys.len(), 2);
                assert_eq!(msg.instructions.len(), 1);
            }
            _ => panic!("Expected Legacy variant"),
        }

        match v0_message {
            VersionedMessage::V0(ref msg) => {
                assert_eq!(msg.header.num_required_signatures, 1);
                assert_eq!(msg.account_keys.len(), 1);
                assert_eq!(msg.address_table_lookups.len(), 1);
            }
            _ => panic!("Expected V0 variant"),
        }
    }

    #[test]
    fn test_versioned_message_default() {
        let message = VersionedMessage::default();

        // Default should be Legacy variant with default LegacyMessage
        match message {
            VersionedMessage::Legacy(ref msg) => {
                assert_eq!(*msg, LegacyMessage::default());
            }
            _ => panic!("Default should be Legacy variant"),
        }
    }

    #[test]
    fn test_versioned_message_clone_and_equality() {
        let legacy_original = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![Pubkey::new_from_array([5u8; 32])],
            recent_blockhash: Hash::new_from_array([123u8; 32]),
            instructions: vec![],
        });

        let v0_original = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 2,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![Pubkey::new_from_array([7u8; 32])],
            recent_blockhash: Hash::new_from_array([200u8; 32]),
            instructions: vec![],
            address_table_lookups: vec![],
        });

        // Test cloning
        let legacy_cloned = legacy_original.clone();
        let v0_cloned = v0_original.clone();

        assert_eq!(legacy_original, legacy_cloned);
        assert_eq!(v0_original, v0_cloned);

        // Test inequality between variants
        assert_ne!(legacy_original, v0_original);
    }

    #[test]
    fn test_versioned_message_legacy_serialization() {
        let legacy_message = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 3,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![
                Pubkey::new_from_array([10u8; 32]),
                Pubkey::new_from_array([20u8; 32]),
                Pubkey::new_from_array([30u8; 32]),
            ],
            recent_blockhash: Hash::new_from_array([123u8; 32]),
            instructions: vec![
                CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![100, 101, 102] },
                CompiledInstruction { program_id_index: 1, accounts: vec![0, 2], data: vec![200, 201] },
            ],
        });

        let serialized = bincode::serialize(&legacy_message).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();

        assert_eq!(legacy_message, deserialized);

        // Verify it's still Legacy variant
        match deserialized {
            VersionedMessage::Legacy(_) => {}
            _ => panic!("Expected Legacy variant after deserialization"),
        }
    }

    #[test]
    fn test_versioned_message_v0_serialization() {
        let v0_message = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 2,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![Pubkey::new_from_array([40u8; 32]), Pubkey::new_from_array([50u8; 32])],
            recent_blockhash: Hash::new_from_array([150u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![42] }],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([99u8; 32]),
                writable_indexes: vec![0, 1],
                readonly_indexes: vec![2, 3, 4],
            }],
        });

        let serialized = bincode::serialize(&v0_message).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();

        assert_eq!(v0_message, deserialized);

        // Verify it's still V0 variant
        match deserialized {
            VersionedMessage::V0(_) => {}
            _ => panic!("Expected V0 variant after deserialization"),
        }
    }

    #[test]
    fn test_versioned_message_variant_compatibility() {
        // Test Legacy variant with various LegacyMessage configurations
        let legacy_variants = vec![
            VersionedMessage::Legacy(LegacyMessage::default()),
            VersionedMessage::Legacy(LegacyMessage {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 1,
                },
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: Hash::new_from_array([1u8; 32]),
                instructions: vec![],
            }),
            VersionedMessage::Legacy(LegacyMessage {
                header: MessageHeader {
                    num_required_signatures: 5,
                    num_readonly_signed_accounts: 2,
                    num_readonly_unsigned_accounts: 3,
                },
                account_keys: vec![
                    Pubkey::new_from_array([10u8; 32]),
                    Pubkey::new_from_array([20u8; 32]),
                    Pubkey::new_from_array([30u8; 32]),
                ],
                recent_blockhash: Hash::new_from_array([255u8; 32]),
                instructions: vec![
                    CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![100] },
                    CompiledInstruction { program_id_index: 1, accounts: vec![0], data: vec![200, 201] },
                ],
            }),
        ];

        // Test V0 variant with various V0Message configurations
        let v0_variants = vec![
            VersionedMessage::V0(V0Message::default()),
            VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 1,
                },
                account_keys: vec![Pubkey::new_from_array([2u8; 32])],
                recent_blockhash: Hash::new_from_array([2u8; 32]),
                instructions: vec![],
                address_table_lookups: vec![],
            }),
            VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 3,
                    num_readonly_signed_accounts: 1,
                    num_readonly_unsigned_accounts: 2,
                },
                account_keys: vec![Pubkey::new_from_array([40u8; 32]), Pubkey::new_from_array([50u8; 32])],
                recent_blockhash: Hash::new_from_array([128u8; 32]),
                instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![42] }],
                address_table_lookups: vec![MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([99u8; 32]),
                    writable_indexes: vec![0, 1],
                    readonly_indexes: vec![2],
                }],
            }),
        ];

        // Test all Legacy variants
        for (i, variant) in legacy_variants.iter().enumerate() {
            let serialized = bincode::serialize(variant).unwrap();
            let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(*variant, deserialized, "Legacy variant {} failed roundtrip", i);

            match deserialized {
                VersionedMessage::Legacy(_) => {}
                _ => panic!("Legacy variant {} became different variant after deserialization", i),
            }
        }

        // Test all V0 variants
        for (i, variant) in v0_variants.iter().enumerate() {
            let serialized = bincode::serialize(variant).unwrap();
            let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(*variant, deserialized, "V0 variant {} failed roundtrip", i);

            match deserialized {
                VersionedMessage::V0(_) => {}
                _ => panic!("V0 variant {} became different variant after deserialization", i),
            }
        }
    }

    #[test]
    fn test_versioned_message_with_mock_generators() {
        // Test with mock Legacy message
        let legacy_message = VersionedMessage::Legacy(generators::generate_mock_legacy_message());

        let serialized = bincode::serialize(&legacy_message).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(legacy_message, deserialized);

        match deserialized {
            VersionedMessage::Legacy(_) => {}
            _ => panic!("Mock Legacy message became different variant"),
        }

        // Test with mock V0 message
        let v0_message = VersionedMessage::V0(generators::generate_mock_v0_message());

        let serialized = bincode::serialize(&v0_message).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(v0_message, deserialized);

        match deserialized {
            VersionedMessage::V0(_) => {}
            _ => panic!("Mock V0 message became different variant"),
        }
    }

    #[test]
    fn test_versioned_message_with_seed_generators() {
        let seeds = [100, 200, 300, 400, 500];

        for seed in seeds {
            // Test Legacy with seed
            let legacy_message = VersionedMessage::Legacy(generators::generate_mock_legacy_message_with_seed(seed));
            let legacy_message2 = VersionedMessage::Legacy(generators::generate_mock_legacy_message_with_seed(seed));
            assert_eq!(legacy_message, legacy_message2, "Legacy message with seed {} not deterministic", seed);

            let serialized = bincode::serialize(&legacy_message).unwrap();
            let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(legacy_message, deserialized);

            // Test V0 with seed
            let v0_message = VersionedMessage::V0(generators::generate_mock_v0_message_with_seed(seed));
            let v0_message2 = VersionedMessage::V0(generators::generate_mock_v0_message_with_seed(seed));
            assert_eq!(v0_message, v0_message2, "V0 message with seed {} not deterministic", seed);

            let serialized = bincode::serialize(&v0_message).unwrap();
            let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
            assert_eq!(v0_message, deserialized);
        }
    }

    #[test]
    fn test_versioned_message_serialization_format_differences() {
        // Create identical content but different variants
        let header = MessageHeader {
            num_required_signatures: 2,
            num_readonly_signed_accounts: 1,
            num_readonly_unsigned_accounts: 1,
        };
        let account_keys = vec![Pubkey::new_from_array([10u8; 32]), Pubkey::new_from_array([20u8; 32])];
        let recent_blockhash = Hash::new_from_array([42u8; 32]);
        let instructions = vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![100] }];

        let legacy_message = VersionedMessage::Legacy(LegacyMessage {
            header: header.clone(),
            account_keys: account_keys.clone(),
            recent_blockhash,
            instructions: instructions.clone(),
        });

        let v0_message = VersionedMessage::V0(V0Message {
            header,
            account_keys,
            recent_blockhash,
            instructions,
            address_table_lookups: vec![], // V0 has this extra field
        });

        let legacy_serialized = bincode::serialize(&legacy_message).unwrap();
        let v0_serialized = bincode::serialize(&v0_message).unwrap();

        // Serialized formats should be different due to version prefix and structure differences
        assert_ne!(legacy_serialized, v0_serialized, "Legacy and V0 serialization should be different");

        // Both should deserialize correctly to their respective variants
        let legacy_deserialized: VersionedMessage = bincode::deserialize(&legacy_serialized).unwrap();
        let v0_deserialized: VersionedMessage = bincode::deserialize(&v0_serialized).unwrap();

        assert_eq!(legacy_message, legacy_deserialized);
        assert_eq!(v0_message, v0_deserialized);

        // Verify variants remain correct
        match legacy_deserialized {
            VersionedMessage::Legacy(_) => {}
            _ => panic!("Legacy message deserialized to wrong variant"),
        }

        match v0_deserialized {
            VersionedMessage::V0(_) => {}
            _ => panic!("V0 message deserialized to wrong variant"),
        }
    }

    #[test]
    fn test_versioned_message_edge_cases() {
        // Test with high values for Legacy (avoid values >= 128 which trigger version prefix detection)
        let max_legacy = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 127,
                num_readonly_signed_accounts: 127,
                num_readonly_unsigned_accounts: 127,
            },
            account_keys: vec![Pubkey::new_from_array([127u8; 32])],
            recent_blockhash: Hash::new_from_array([127u8; 32]),
            instructions: vec![CompiledInstruction {
                program_id_index: 127,
                accounts: vec![127, 126, 125],
                data: vec![127, 126, 125, 124],
            }],
        });

        let serialized = bincode::serialize(&max_legacy).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(max_legacy, deserialized);

        // Test with high values for V0 (V0 messages have version prefix constraints)
        let max_v0 = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 100,
                num_readonly_signed_accounts: 100,
                num_readonly_unsigned_accounts: 100,
            },
            account_keys: vec![Pubkey::new_from_array([200u8; 32])],
            recent_blockhash: Hash::new_from_array([200u8; 32]),
            instructions: vec![CompiledInstruction {
                program_id_index: 200,
                accounts: vec![200, 199, 198],
                data: vec![200, 199, 198, 197],
            }],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([200u8; 32]),
                writable_indexes: vec![200, 199],
                readonly_indexes: vec![198, 197, 196],
            }],
        });

        let serialized = bincode::serialize(&max_v0).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(max_v0, deserialized);

        // Test with zero values
        let zero_legacy = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 0,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![Pubkey::new_from_array([0u8; 32])],
            recent_blockhash: Hash::new_from_array([0u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![0], data: vec![0] }],
        });

        let serialized = bincode::serialize(&zero_legacy).unwrap();
        let deserialized: VersionedMessage = bincode::deserialize(&serialized).unwrap();
        assert_eq!(zero_legacy, deserialized);
    }
}
